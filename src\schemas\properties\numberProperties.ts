import { NUMBER_LIMIT } from "@/lib/constants";
import { z } from "zod";

export const numberPropertiesSchema = z
  .object({
    currency: z.boolean(),
    minimumValue: z.number({ message: "Enter minimum value" }).min(NUMBER_LIMIT.MINIMUM, { message: `Minimum value is ${NUMBER_LIMIT.MINIMUM}` }),
    maximumValue: z.number({ message: "Enter maximum value" }).max(NUMBER_LIMIT.MAXIMUM, { message: `Maximum value is ${NUMBER_LIMIT.MAXIMUM}` }),
  })
  .superRefine(({ minimumValue, maximumValue }, ctx) => {
    if (minimumValue >= maximumValue) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Minimum value must be less than maximum value",
        path: ["minimumValue"],
      });
    }
  });
