import AudioRecording<PERSON>ield from "@/components/basics/Audio/AudioRecordingField";
import <PERSON><PERSON>ield from "@/components/basics/Camera/CameraField";
import DateField from "@/components/basics/Date/DateField";
import DropdownField from "@/components/basics/Dropdown/DropdownField";
import FileField from "@/components/basics/File/FileField";
import MultipleChoiceField from "@/components/basics/MultipleChoice/MultipleChoiceField";
import NumberField from "@/components/basics/Number/NumberField";
import ParagraphField from "@/components/basics/Paragraph/ParagraphField";
import PhoneNumberField from "@/components/basics/PhoneNumber/PhoneNumberField";
import RatingField from "@/components/basics/Rating/RatingField";
import ReadOnlyField from "@/components/basics/ReadOnly/ReadOnlyField";
import ShortAnswerField from "@/components/basics/ShortAnswer/ShortAnswerField";
import SingleChoiceField from "@/components/basics/SingleChoice/SingleChoiceField";
import TimeField from "@/components/basics/Time/TimeField";
import type { BaseFieldProps, ElementType, FormElementProperties } from "@/components/types";

export const FIELD_COMPONENTS: Record<ElementType, React.FC<BaseFieldProps>> = {
  // SectionLayout,
  ShortAnswerField,
  ParagraphField,
  NumberField,
  PhoneNumberField,
  DateField,
  TimeField,
  SingleChoiceField,
  MultipleChoiceField,
  FileField,
  CameraField,
  AudioRecordingField,
  RatingField,
  DropdownField,
  ReadOnlyField,
};

export const VALID_AUDIO_FORMATS: string[] = ["mp3", "wav"];
export const VALID_READ_ONLY_FILE_FORMATS: string[] = ["mp3", "mp4", "jpg", "png", "jpeg", "avi", "aac", "m4a", "mpeg", "wav"];
export const AUDIO_UPLOAD_LIMIT = 2 * 1024 * 1024;
export const READ_ONLY_FILE_UPLOAD_LIMIT = 5 * 1024 * 1024;
export const PRIMARY_COLOUR_GREEN = "#59C903";

export const DEFAULT_BASIC_PROPERTIES: FormElementProperties = {
  label: "",
  placeholder: "",
  hint: "",
  tooltip: "",
  tag: "",
  required: true,
  validate: false,
};

export const DEFAULT_BASIC_PROPERTIES_WITHOUT_VALIDATE: FormElementProperties = {
  label: "",
  placeholder: "",
  hint: "",
  tooltip: "",
  tag: "",
  required: true,
};

export const DEFAULT_SHORT_ANSWER_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  minimumCharacterCount: 1,
  maximumCharacterCount: 50,
};

export const DEFAULT_PARAGRAPH_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  minimumCharacterCount: 1,
  maximumCharacterCount: 1000,
};

export const DEFAULT_NUMBER_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  minimumValue: 0,
  maximumValue: 4,
};

export const DEFAULT_SINGLE_CHOICE_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  options: ["", ""],
  addOther: false,
};

export const DEFAULT_MULTIPLE_CHOICE_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  options: ["", ""],
  addOther: false,
};

export const DEFAULT_FILE_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES_WITHOUT_VALIDATE,
  minimumFileSize: 0,
  maximumFileSize: 2,
  file: {
    type: "Image",
    formats: [],
  },
  allowMultipleFiles: false,
};

export const DEFAULT_CAMERA_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES_WITHOUT_VALIDATE,
  mediaType: "Image",
};
export const DEFAULT_PHONE_NUMBER_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  includeCountryCode: false,
};
export const DEFAULT_DATE_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  dateFormat: "DD-MM-YYYY",
  allowableDate: "All Dates",
};
export const DEFAULT_TIME_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
};
export const DEFAULT_AUDIO_RECORDING_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES_WITHOUT_VALIDATE,
  minimumDuration: "00:00:01",
  maximumDuration: "00:20:00",
};
export const DEFAULT_RATING_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  numbersOfRating: 5,
  lowestRating: "Very Poor",
  highestRating: "Very Good",
};
export const DEFAULT_DROPDOWN_PROPERTIES: FormElementProperties = {
  ...DEFAULT_BASIC_PROPERTIES,
  options: ["", ""],
  correctAnswer: "",
};
export const DEFAULT_READ_ONLY_PROPERTIES: FormElementProperties = {
  title: "",
  category: "Title Only",
};

export const COMPONENT_PROPERTIES_MAPPING: Record<ElementType, FormElementProperties> = {
  ShortAnswerField: DEFAULT_SHORT_ANSWER_PROPERTIES,
  ParagraphField: DEFAULT_PARAGRAPH_PROPERTIES,
  NumberField: DEFAULT_NUMBER_PROPERTIES,
  PhoneNumberField: DEFAULT_PHONE_NUMBER_PROPERTIES,
  DateField: DEFAULT_DATE_PROPERTIES,
  TimeField: DEFAULT_TIME_PROPERTIES,
  SingleChoiceField: DEFAULT_SINGLE_CHOICE_PROPERTIES,
  MultipleChoiceField: DEFAULT_MULTIPLE_CHOICE_PROPERTIES,
  FileField: DEFAULT_FILE_PROPERTIES,
  CameraField: DEFAULT_CAMERA_PROPERTIES,
  AudioRecordingField: DEFAULT_AUDIO_RECORDING_PROPERTIES,
  RatingField: DEFAULT_RATING_PROPERTIES,
  DropdownField: DEFAULT_DROPDOWN_PROPERTIES,
  ReadOnlyField: DEFAULT_READ_ONLY_PROPERTIES,
};

export const SHORT_ANSWER_CHARACTER_COUNT = {
  MINIMUM: 0,
  MAXIMUM: 250,
};

export const PARAGRAPH_CHARACTER_COUNT = {
  MINIMUM: 0,
  MAXIMUM: 65000,
};

export const NUMBER_LIMIT = {
  MINIMUM: 0,
  MAXIMUM: 999_999_999,
};

export const FILE_SIZE = {
  MINIMUM: 0,
  MAXIMUM: 5,
};

export const IMAGE_SIZE = {
  MINIMUM: 0,
  MAXIMUM: 5,
};

export const TIME_REGEX = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;

export const TAG_REGEX = /\s+/g;
export const TAG_REPLACEMENT = "_";

export const MAX_VIDEO_DURATION = "00:01:00";

export const MAX_AUDIO_RECORDING_DURATION = "01:30:00";
export const MIN_AUDIO_RECORDING_DURATION = "00:00:01";

export const COUNTRY_CODES: string[] = ["Nigeria (+234)"];

export const DATE_FORMATS = ["DD-MM-YYYY", "MM-DD-YYYY"];
export const ALLOWABLE_DATES = ["All Dates", "Future Date", "Past Date", "Date Range"];

export const AUDIO_RECORDING_DURATION_COUNT = {
  MINIMUM: 1,
  MAXIMUM: 30,
};

export const NUMBERS_OF_RATINGS = 10;

export const DROP_DOWN_OPTIONS = ["Option 1", "Option 2", "Option 3"];

export const READ_ONLY_CATEGORY = ["Title Only", "File Only", "Title & File"];

export const PAGE_SIZES = [20, 50, 100];
