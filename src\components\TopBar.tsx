import { fredoka } from "@/app/fonts";
import BetterLifeLogo from "@/assets/images/better-life-logo.svg";
import DummyCompanyLogo from "@/assets/images/dummy-company-logo.svg";
import Image from "next/image";
import { TopBarProps } from "@/components/types";

const TopBar = ({ pageTitle }: TopBarProps) => {
  return (
    <div className="fixed left-0 top-0 z-10 flex h-20 w-full items-center justify-between bg-white p-4">
      <Image src={DummyCompanyLogo} alt="Better Life Image" className="h-10 w-10" />
      <div className={`${fredoka.className} text-lg font-semibold`}>{pageTitle}</div>
      <Image src={BetterLifeLogo} alt="Better Life Image" className="h-10 w-10" />
    </div>
  );
};

export default TopBar;
