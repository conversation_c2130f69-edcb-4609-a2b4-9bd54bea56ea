import { PhoneNumberPropertiesFormData } from "@/app/(core)/forms/types";
import { FormElement } from "@/components/types";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { useGetSingleForm } from "@/hooks/tansack-query/queries/use-forms";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { phoneNumberPropertiesSchema } from "@/schemas/properties/phoneNumberProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const PhoneNumberProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const formId = useAppSelector(state => state.form.formId);
  const elementId = selectedFormBuilderItem?.id || "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) || "";
  }

  const { singleFormData } = useGetSingleForm(formId);

  const form = useForm<PhoneNumberPropertiesFormData>({
    resolver: zodResolver(phoneNumberPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      includeCountryCode: selectedFormBuilderItem?.includeCountryCode,
      countryCode: selectedFormBuilderItem?.countryCode,
    },
    shouldFocusError: false,
  });

  const {
    getValues,
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("includeCountryCode", selectedFormBuilderItem?.includeCountryCode || false);
    setValue("countryCode", selectedFormBuilderItem?.countryCode || "Nigeria(+234)");
  }, [selectedFormBuilderItem]);

  const applyChanges = (data: PhoneNumberPropertiesFormData) => {
    const currentData = { ...getValues() };
    if (!currentData.includeCountryCode) {
      delete currentData["countryCode"];
    }
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...currentData,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="includeCountryCode"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center gap-1">
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel>Include Country Code</FormLabel>
                </div>
              </FormItem>
            )}
          />
          {getValues().includeCountryCode && (
            <div className="h-[3rem] rounded-[10px] border border-primary-gray bg-primary-gray/10 p-3">{singleFormData?.country?.phone_code}</div>
          )}
        </div>
      </form>
    </Form>
  );
};

export default PhoneNumberProperties;
