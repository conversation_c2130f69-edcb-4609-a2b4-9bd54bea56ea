"use client";
import { SingleFormData } from "@/app/(core)/forms/types";
import { fredoka } from "@/app/fonts";
import nameSortIcon from "@/assets/icons/name-sort-icon.svg";
import searchIcon from "@/assets/icons/search.svg";
import sortIcon from "@/assets/icons/sort-icon.svg";
import CreateFromExistingFormLoader from "@/components/CreateFromExistingFormLoader";
import PageWrapper from "@/components/PageWrapper";
import ExistingFormsSkeleton from "@/components/skeletons/ExistingFormsSkeleton";
import TablePagination from "@/components/TablePagination";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useUpdateForm } from "@/hooks/tansack-query/mutations/use-forms";
import { useGetExistingForms } from "@/hooks/tansack-query/queries/use-forms";
import useDebounce from "@/hooks/use-debounce";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { PAGE_SIZES } from "@/lib/constants";
import { replaceFormScreens } from "@/lib/redux/slices/formSlice";
import { formatScreensForUI } from "@/lib/utils";
import * as changeCase from "change-case";
import Image from "next/image";
import { parseAsInteger, useQueryState } from "nuqs";
import { useEffect } from "react";

const ExistingForms = () => {
  const [query, setQuery] = useQueryState("q", { defaultValue: "" });
  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));
  const [pageSize, setPageSize] = useQueryState("pageSize", parseAsInteger.withDefault(PAGE_SIZES[0]));
  const [sort, setSort] = useQueryState("sort");
  const dispatch = useAppDispatch();
  const formId = useAppSelector(state => state.form.formId);

  const search = useDebounce<string>(query.trim());
  let sortParam = undefined;
  if (sort) {
    const sortParamArr = sort.split(",");
    sortParam = sortParamArr[1] === "asc" ? 1 : -1;
  }

  const { existingFormsLoading, existingForms } = useGetExistingForms({
    search,
    page,
    pageSize,
    sort: sortParam,
  });
  const { updateSingleForm, isUpdatingSingleForm, isUpdateSingleFormSuccess, updateSingleFormData } = useUpdateForm();
  const existingFormsData = existingForms?.data;

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
  };

  const handleSort = (key: string) => {
    setSort(value => {
      if (value === `${key},asc`) {
        return `${key},desc`;
      }
      return `${key},asc`;
    });
  };

  const handleCreateExistingForm = (form: SingleFormData) => {
    updateSingleForm({
      screens: form.screens,
    });
  };

  const handleBack = () => {
    window.location.replace(sessionStorage.getItem("returnUrl") || "");
  };

  useEffect(() => {
    if (isUpdateSingleFormSuccess && updateSingleFormData) {
      const formattedScreens = formatScreensForUI(updateSingleFormData.data.data?.screens);
      dispatch(replaceFormScreens(formattedScreens));
      window.location.href = `/forms/${formId}?returnUrl=${sessionStorage.getItem("returnUrl") || ""}`;
    }
  }, [isUpdateSingleFormSuccess]);

  return (
    <PageWrapper pageTitle="Create New Form">
      <div className="px-[10rem]">
        <p className={`${fredoka.className} mt-[8rem] text-lg font-semibold`}>Select Existing Form</p>
        <div className="mb-4 mt-2 flex justify-between">
          <div className="relative">
            <div>
              <Input
                placeholder="Search by Name"
                className="h-[2rem] w-[1074px] border-[#75748F00] pr-8"
                value={query}
                onChange={e => setQuery(e.target.value)}
              />
              <Image src={searchIcon} alt="Search Icon" className="absolute bottom-1/2 right-6 translate-y-1/2" />
            </div>
          </div>
          <div className="relative">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex h-[2rem] gap-2 rounded-[5px] bg-primary-green text-white">
                  <p>Sort By</p>
                  <Image src={sortIcon} alt="Icon for sorting options" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="z-0 mt-2.5 w-[12.5rem] p-2 shadow-md" align="start">
                <div
                  className="flex cursor-pointer justify-between p-2 hover:border hover:border-primary-green active:bg-primary-green active:text-white"
                  onClick={() => handleSort("name")}
                >
                  <p>Name</p>
                  <Image src={nameSortIcon} alt="Sort A to Z or Z to A" />
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div>
          {existingFormsLoading ? (
            <ExistingFormsSkeleton />
          ) : existingFormsData?.length === 0 ? (
            <div></div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Form ID</TableHead>
                    <TableHead>Form Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Entity Type</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {existingFormsData?.map(form => {
                    return (
                      <TableRow
                        key={form.id}
                        className="hover:cursor-pointer hover:border-primary-orange hover:bg-secondary-yellow"
                        onClick={() => handleCreateExistingForm(form)}
                      >
                        <TableCell>{form.id}</TableCell>
                        <TableCell>{form.name}</TableCell>
                        <TableCell>{form.description}</TableCell>
                        <TableCell>{changeCase.capitalCase(form.entity_type || "")}</TableCell>
                        {/* <TableCell>{form.entity_type}</TableCell> */}
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
              <div className="mt-4">
                {existingFormsData && (
                  <TablePagination
                    count={pageSize}
                    currentPage={existingForms.currentPage}
                    totalPages={existingForms.totalPages}
                    pageSizes={PAGE_SIZES}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                  />
                )}
              </div>
              <div className="mb-4 mt-8 flex justify-end">
                <Button className={`h-[3.4rem] w-40 ${fredoka.className} text-lg font-semibold`} variant="yellow" onClick={handleBack}>
                  Back
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
      {isUpdatingSingleForm && <CreateFromExistingFormLoader />}
    </PageWrapper>
  );
};

export default ExistingForms;
