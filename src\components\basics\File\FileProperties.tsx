import { FilePropertiesFormData } from "@/app/(core)/forms/types";
import NumberInput from "@/components/NumberInput";
import { FormElement } from "@/components/types";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { filePropertiesSchema } from "@/schemas/properties/fileProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { Fragment, useEffect } from "react";
import { useForm } from "react-hook-form";

const FileProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id || "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) || "";
  }

  const videoFormats = ["mp4", "mpeg", "avi"];
  const audioFormats = ["mp3", "aac", "wav"];
  const documentFormats = ["pdf", "doc", "docx", "xls", "xlsx", "csv"];
  const imageFormats = ["jpg", "jpeg", "png"];
  const allFileFormats = [...videoFormats, ...audioFormats, ...documentFormats, ...imageFormats];
  const fileTypes = ["Image", "Video", "Audio", "Document"];

  const form = useForm<FilePropertiesFormData>({
    resolver: zodResolver(filePropertiesSchema),
    mode: "onChange",
    defaultValues: {
      minimumFileSize: selectedFormBuilderItem?.minimumFileSize || 0,
      maximumFileSize: selectedFormBuilderItem?.maximumFileSize || 2,
      fileType: selectedFormBuilderItem?.file?.type,
      mp4: selectedFormBuilderItem?.file?.formats.includes("mp4") || false,
      mpeg: selectedFormBuilderItem?.file?.formats.includes("mpeg") || false,
      avi: selectedFormBuilderItem?.file?.formats.includes("avi") || false,
      pdf: selectedFormBuilderItem?.file?.formats.includes("pdf") || false,
      doc: selectedFormBuilderItem?.file?.formats.includes("doc") || false,
      docx: selectedFormBuilderItem?.file?.formats.includes("docx") || false,
      xls: selectedFormBuilderItem?.file?.formats.includes("xls") || false,
      xlsx: selectedFormBuilderItem?.file?.formats.includes("xlsx") || false,
      csv: selectedFormBuilderItem?.file?.formats.includes("csv") || false,
      mp3: selectedFormBuilderItem?.file?.formats.includes("mp3") || false,
      wav: selectedFormBuilderItem?.file?.formats.includes("wav") || false,
      aac: selectedFormBuilderItem?.file?.formats.includes("aac") || false,
      jpg: selectedFormBuilderItem?.file?.formats.includes("jpg") || false,
      png: selectedFormBuilderItem?.file?.formats.includes("png") || false,
      allowMultipleFiles: selectedFormBuilderItem?.allowMultipleFiles || false,
    },
    shouldFocusError: false,
  });

  const {
    getValues,
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("minimumFileSize", selectedFormBuilderItem?.minimumFileSize || 0);
    setValue("maximumFileSize", selectedFormBuilderItem?.maximumFileSize || 2);
    setValue("fileType", selectedFormBuilderItem?.file?.type || "Video");
    setValue("mp4", selectedFormBuilderItem?.file?.formats.includes("mp4") || false);
    setValue("mpeg", selectedFormBuilderItem?.file?.formats.includes("mpeg") || false);
    setValue("avi", selectedFormBuilderItem?.file?.formats.includes("avi") || false);
    setValue("pdf", selectedFormBuilderItem?.file?.formats.includes("pdf") || false);
    setValue("doc", selectedFormBuilderItem?.file?.formats.includes("doc") || false);
    setValue("docx", selectedFormBuilderItem?.file?.formats.includes("docx") || false);
    setValue("xls", selectedFormBuilderItem?.file?.formats.includes("xls") || false);
    setValue("xlsx", selectedFormBuilderItem?.file?.formats.includes("xlsx") || false);
    setValue("csv", selectedFormBuilderItem?.file?.formats.includes("csv") || false);
    setValue("mp3", selectedFormBuilderItem?.file?.formats.includes("mp3") || false);
    setValue("wav", selectedFormBuilderItem?.file?.formats.includes("wav") || false);
    setValue("aac", selectedFormBuilderItem?.file?.formats.includes("aac") || false);
    setValue("jpg", selectedFormBuilderItem?.file?.formats.includes("jpg") || false);
    setValue("png", selectedFormBuilderItem?.file?.formats.includes("png") || false);
    setValue("allowMultipleFiles", selectedFormBuilderItem?.allowMultipleFiles || false);
  }, [selectedFormBuilderItem]);

  const applyChanges = (data: FilePropertiesFormData) => {
    const currentData = getValues();
    const trueFormData = Object.entries(currentData)
      .filter(([_, value]) => value === true)
      .map(([key]) => key);

    const fileFormats = trueFormData.filter(item => allFileFormats.includes(item));

    const newFormData = {
      minimumFileSize: currentData.minimumFileSize,
      maximumFileSize: currentData.maximumFileSize,
      file: {
        type: currentData.fileType,
        formats: fileFormats,
      },
      allowMultipleFiles: currentData.allowMultipleFiles,
    };

    const newFormElement = {
      ...selectedFormBuilderItem,
      ...newFormData,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="minimumFileSize"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum File Size (MB)</FormLabel>
                <FormControl>
                  <NumberInput {...field} onChange={field.onChange} className={`${errors.minimumFileSize && "border-destructive"}`} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="maximumFileSize"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maximum File Size (MB)</FormLabel>
                <FormControl>
                  <NumberInput {...field} onChange={field.onChange} className={`${errors.maximumFileSize && "border-destructive"}`} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="fileType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>File Type</FormLabel>
                <Select
                  onValueChange={value => {
                    field.onChange(value);
                    // Reset all format checkboxes to false
                    allFileFormats.forEach(format => {
                      setValue(format as keyof FilePropertiesFormData, false);
                    });
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {fileTypes.map(type => (
                      <SelectItem value={type} key={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="mb-2 mt-2 grid grid-cols-2 gap-y-4">
            {getValues().fileType === "Image"
              ? imageFormats.map(item => (
                  <Fragment key={item}>
                    <FormField
                      control={form.control}
                      name={item as keyof FilePropertiesFormData}
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex flex-row items-center gap-1">
                            <FormControl>
                              <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <FormLabel>{item}</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </Fragment>
                ))
              : getValues().fileType === "Video"
                ? videoFormats.map(item => (
                    <Fragment key={item}>
                      <FormField
                        control={form.control}
                        name={item as keyof FilePropertiesFormData}
                        render={({ field }) => (
                          <FormItem>
                            <div className="flex flex-row items-center gap-1">
                              <FormControl>
                                <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                              <FormLabel>{item}</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </Fragment>
                  ))
                : getValues().fileType === "Audio"
                  ? audioFormats.map(item => (
                      <Fragment key={item}>
                        <FormField
                          control={form.control}
                          name={item as keyof FilePropertiesFormData}
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex flex-row items-center gap-1">
                                <FormControl>
                                  <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                                <FormLabel>{item}</FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />
                      </Fragment>
                    ))
                  : documentFormats.map(item => (
                      <Fragment key={item}>
                        <FormField
                          control={form.control}
                          name={item as keyof FilePropertiesFormData}
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex flex-row items-center gap-1">
                                <FormControl>
                                  <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                                <FormLabel>{item}</FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />
                      </Fragment>
                    ))}
          </div>
          <FormField
            control={form.control}
            name="allowMultipleFiles"
            render={({ field }) => (
              <FormItem>
                <div className="flex flex-row items-center gap-1">
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel>Allow Multiple Files</FormLabel>
                </div>
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default FileProperties;
