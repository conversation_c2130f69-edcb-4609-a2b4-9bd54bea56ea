"use client";
import { CONFIG_FRONTEND_URL } from "@/config/env";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { updateUserTokens } from "@/lib/redux/slices/authSlice";
import { useQueryState } from "nuqs";
import type { ComponentType } from "react";
import SomethingWentWrong from "./SomethingWentWrong";

export default function AuthWrapper<P extends object>(Component: ComponentType<P>) {
  return function IsAuth(props: P) {
    const dispatch = useAppDispatch();
    const [returnUrl, setReturnUrl] = useQueryState("returnUrl", { defaultValue: "" });
    const [accessTokenParam, setAccessTokenParam] = useQueryState("accessToken", { defaultValue: "" });
    const [refreshTokenParam, setRefreshTokenParam] = useQueryState("refreshToken", { defaultValue: "" });
    const isSomethingWrong = useAppSelector(state => state.auth.isSomethingWrong);
    const accessToken = useAppSelector(state => state.auth.userTokens.accessToken);
    const refreshToken = useAppSelector(state => state.auth.userTokens.refreshToken);
    const hasTokens = (accessTokenParam && refreshTokenParam) || (accessToken && refreshToken);

    if (returnUrl) {
      sessionStorage.setItem("returnUrl", decodeURIComponent(returnUrl));
    }

    if (accessTokenParam && refreshTokenParam) {
      dispatch(updateUserTokens({ accessToken: accessTokenParam, refreshToken: refreshTokenParam }));
      setAccessTokenParam(null); // Remove accessToken param
      setRefreshTokenParam(null); // Remove refreshToken param
    }

    if (!hasTokens && window !== undefined) {
      return window.location.replace(CONFIG_FRONTEND_URL as string);
    }

    if (isSomethingWrong) {
      return <SomethingWentWrong />;
    }

    return <Component {...props} />;
  };
}
