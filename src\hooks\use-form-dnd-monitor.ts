import { FormElement, FormElementProperties, FormScreen } from "@/components/types";
import { useAppDispatch } from "@/hooks/use-redux";
import { replaceScreenSections, replaceSectionElements } from "@/lib/redux/slices/formSlice";
import { createFormElement, createFormSection, getElementType } from "@/lib/utils";
import { DragEndEvent, DragOverEvent, useDndMonitor } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";

const useFormDndMonitor = (formScreens: FormScreen[]) => {
  const dispatch = useAppDispatch();
  useDndMonitor({
    onDragEnd: (event: DragEndEvent) => {
      const { active, over } = event;
      if (!active || !over) return;
      const activeId = active.id;
      const overId = over.id;

      //! 1. Dropping menu section into a screen
      if (activeId.toString().startsWith("menu-element") && overId.toString().startsWith("screen") && active.data.current?.isSection) {
        const overScreenId: string = over.data.current?.screenId;
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!overScreen) return;
        const newScreenSections = [...overScreen.sections, createFormSection()];
        dispatch(replaceScreenSections({ screenId: overScreenId, sections: newScreenSections }));
      }

      //! 2. Dropping menu section over section in a screen
      if (activeId.toString().startsWith("menu-element") && overId.toString().startsWith("section") && active.data.current?.isSection) {
        const overScreenId: string = over.data.current?.screenId;
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!overScreen) return;
        const newScreenSections = [...overScreen.sections];
        const overSectionIndex = newScreenSections.findIndex(section => section.id === overId);
        if (overSectionIndex === -1) return;
        newScreenSections.splice(overSectionIndex, 0, createFormSection());
        dispatch(replaceScreenSections({ screenId: overScreenId, sections: newScreenSections }));
      }

      //! 3. Dropping menu element into a section
      if (activeId.toString().startsWith("menu-element") && overId.toString().startsWith("section") && !active.data.current?.isSection) {
        const { elementName, element } = active.data.current as { elementName: string; element: FormElementProperties };
        const overScreenId: string = over.data.current?.screenId;
        const overSectionId: string = over.data.current?.section.id;
        const elementType = getElementType(elementName);
        const properties: Omit<FormElement, "id"> = {
          type: elementType,
          ...element,
        };
        const newFormElement = createFormElement(properties);
        const overSectionElements: FormElement[] = [...over.data.current?.section.elements];
        overSectionElements.push(newFormElement);
        dispatch(replaceSectionElements({ screenId: overScreenId, sectionId: overSectionId, elements: overSectionElements }));
      }

      //! 4. Dropping menu element over element in a section
      if (activeId.toString().startsWith("menu-element") && !active.data.current?.isSection && over.data.current?.isFormElement) {
        const { elementName, element } = active.data.current as { elementName: string; element: FormElementProperties };
        const elementType = getElementType(elementName);
        const properties: Omit<FormElement, "id"> = {
          type: elementType,
          ...element,
        };
        const newFormElement = createFormElement(properties);
        const overScreenId: string = over.data.current?.screenId;
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!overScreen) return;
        const overSectionId: string = over.data.current?.sectionId;
        const overSection = overScreen.sections.find(section => section.id === overSectionId);
        if (!overSection) return;
        const overElementIndex = overSection.elements.findIndex(el => el.id === overId);
        if (overElementIndex === -1) return;
        const overSectionElements: FormElement[] = [...overSection.elements];
        overSectionElements.splice(overElementIndex, 0, newFormElement);
        dispatch(replaceSectionElements({ screenId: overScreenId, sectionId: overSectionId, elements: overSectionElements }));
      }

      //! 5. Dropping section from a screen into another screen
      if (activeId.toString().startsWith("section") && overId.toString().startsWith("screen")) {
        const activeScreenId = active.data.current?.screenId;
        const overScreenId = over.data.current?.screenId;
        if (!activeScreenId || !overScreenId) return;

        if (activeScreenId === overScreenId) return;

        const activeScreen = formScreens.find(screen => screen.id === activeScreenId);
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!activeScreen || !overScreen) return;

        const activeSections = activeScreen.sections;
        const overSections = overScreen.sections;

        if (activeSections.length === 1) return; // A screen must have at least one section

        const activeSection = active.data.current?.section;
        const newOverSections = [...overSections];

        const newActiveSections = activeSections.filter(section => section.id !== activeId);
        newOverSections.splice(overSections.length, 0, activeSection);

        dispatch(replaceScreenSections({ screenId: activeScreenId, sections: newActiveSections }));
        dispatch(replaceScreenSections({ screenId: overScreenId, sections: newOverSections }));
      }

      //! 6. Dropping element from a section into another section
      if (active.data.current?.isFormElement && overId.toString().startsWith("section")) {
        const activeScreenId = active.data.current?.screenId;
        const activeSectionId = active.data.current?.sectionId;
        const overScreenId = over.data.current?.screenId;

        if (activeSectionId === overId) return;

        const activeScreen = formScreens.find(screen => screen.id === activeScreenId);
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!activeScreen || !overScreen) return;

        const activeSection = activeScreen.sections.find(section => section.id === activeSectionId);
        const overSection = overScreen.sections.find(section => section.id === overId);

        if (!activeSection || !overSection) return;

        const activeElements = activeSection.elements;
        const overElements = overSection.elements;

        const activeElement = active.data.current?.element;
        const newOverElements = [...overElements];

        const newActiveElements = activeElements.filter(element => element.id !== activeId);
        newOverElements.splice(overElements.length, 0, activeElement);

        dispatch(replaceSectionElements({ screenId: activeScreenId, sectionId: activeSectionId, elements: newActiveElements }));
        dispatch(replaceSectionElements({ screenId: overScreenId, sectionId: String(overId), elements: newOverElements }));
      }
    },

    onDragOver: (event: DragOverEvent) => {
      const { active, over } = event;
      if (!active || !over) return;
      const activeId = active.id;
      const overId = over.id;

      //! 1. Dropping section from screen over section in a screen
      if (activeId.toString().startsWith("section") && overId.toString().startsWith("section")) {
        const activeScreenId = active.data.current?.screenId;
        const overScreenId = over.data.current?.screenId;
        if (!activeScreenId || !overScreenId) return;

        const activeScreen = formScreens.find(screen => screen.id === activeScreenId);
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!activeScreen || !overScreen) return;
        const activeSections = activeScreen.sections;
        const overSections = overScreen.sections;

        const activeSectionIndex = activeSections.findIndex(section => section.id === activeId);
        const overSectionIndex = overSections.findIndex(section => section.id === overId);

        if (activeSectionIndex === -1 || overSectionIndex === -1) return;

        if (activeScreenId === overScreenId) {
          const newSections = arrayMove(activeSections, activeSectionIndex, overSectionIndex);
          dispatch(replaceScreenSections({ screenId: activeScreenId, sections: newSections }));
        } else {
          if (activeSections.length === 1) return; // A screen must have at least one section
          const activeSection = active.data.current?.section;
          const newOverSections = [...overSections];

          const newActiveSections = activeSections.filter(section => section.id !== activeId);
          newOverSections.splice(overSectionIndex, 0, activeSection);

          dispatch(replaceScreenSections({ screenId: activeScreenId, sections: newActiveSections }));
          dispatch(replaceScreenSections({ screenId: overScreenId, sections: newOverSections }));
        }
      }

      //! 2. Dropping element from section over element in a section
      if (active.data.current?.isFormElement && over.data.current?.isFormElement) {
        const activeScreenId = active.data.current?.screenId;
        const overScreenId = over.data.current?.screenId;
        const activeSectionId = active.data.current?.sectionId;
        const overSectionId = over.data.current?.sectionId;

        const activeScreen = formScreens.find(screen => screen.id === activeScreenId);
        const overScreen = formScreens.find(screen => screen.id === overScreenId);
        if (!activeScreen || !overScreen) return;

        const activeSection = activeScreen.sections.find(section => section.id === activeSectionId);
        const overSection = overScreen.sections.find(section => section.id === overSectionId);
        if (!activeSection || !overSection) return;

        const activeElements = activeSection.elements;
        const overElements = overSection.elements;

        const activeElementIndex = activeElements.findIndex(element => element.id === activeId);
        const overElementIndex = overElements.findIndex(element => element.id === overId);

        if (activeElementIndex === -1 || overElementIndex === -1) return;

        if (activeSectionId === overSectionId) {
          const newElements = arrayMove(activeElements, activeElementIndex, overElementIndex);
          dispatch(replaceSectionElements({ screenId: activeScreenId, sectionId: activeSectionId, elements: newElements }));
        } else {
          const activeElement = active.data.current?.element;
          const newOverElements = [...overElements];

          const newActiveElements = activeElements.filter(element => element.id !== activeId);
          newOverElements.splice(overElementIndex, 0, activeElement);
          dispatch(replaceSectionElements({ screenId: activeScreenId, sectionId: activeSectionId, elements: newActiveElements }));
          dispatch(replaceSectionElements({ screenId: overScreenId, sectionId: overSectionId, elements: newOverElements }));
        }
      }
    },
  });
};

export default useFormDndMonitor;
