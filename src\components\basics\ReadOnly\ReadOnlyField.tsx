import { blogger<PERSON><PERSON>, fredoka } from "@/app/fonts";
import DeleteIcon from "@/assets/icons/delete-icon.svg";
import FileUploadIcon from "@/assets/icons/file-upload.svg";
import UploadIcon from "@/assets/icons/upload.svg";
import type { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { useUploadReadonlyFile } from "@/hooks/tansack-query/mutations/use-forms";
import { useToast } from "@/hooks/use-toast";
import { AUDIO_UPLOAD_LIMIT, READ_ONLY_FILE_UPLOAD_LIMIT, VALID_READ_ONLY_FILE_FORMATS } from "@/lib/constants";
import { getFileFormat, updateFormElementOnInputChange } from "@/lib/utils";
import Image from "next/image";
import { useRef, useState } from "react";

const ReadOnlyField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { category, title, fileUrl } = element;
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragging, setDragging] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const { uploadReadonlyFile, isUploadingReadonlyFile } = useUploadReadonlyFile(screenId, sectionId, setUploadProgress);
  const fileName = fileUrl?.slice(fileUrl?.lastIndexOf("/") + 1);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ title: e.target.value }, screenId, sectionId);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
    handleFileSelection(e.dataTransfer.files);
  };

  const handleDeleteFile = () => {
    updateFormElementOnInputChange({ fileUrl: "" }, screenId, sectionId);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileSelection(e.target.files);
    }
  };

  const handleFileSelection = (filesList: FileList) => {
    if (filesList.length > 1) {
      toast({ description: "You can only upload one file" });
      return;
    }

    const selectedFile = filesList[0];

    if (!selectedFile) {
      return; // No file selected
    }

    if (selectedFile.size > READ_ONLY_FILE_UPLOAD_LIMIT) {
      toast({
        description: `You cannot upload a file greater than ${READ_ONLY_FILE_UPLOAD_LIMIT / (1024 * 1024)}MB`,
      });
      return;
    }

    const format = getFileFormat(selectedFile.name);

    if (!VALID_READ_ONLY_FILE_FORMATS.includes(format)) {
      toast({
        description: `Unsupported file format. Please upload a valid file. (allowed formats: ${VALID_READ_ONLY_FILE_FORMATS.join(", .")})`,
      });
      return;
    }

    uploadReadonlyFile(selectedFile);
  };

  const renderTitleOnly = () => (
    <div className="relative">
      <div className="px-6 pb-10 pt-10">
        <div className="space-y-4">
          <div className="flex items-start gap-2">
            <AutosizeTextarea
              className="w-auto border-none bg-transparent p-0"
              placeholder="Enter read-only text"
              value={title || ""}
              onChange={handleTitleChange}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderFileOnly = () => (
    <div className="relative">
      <div className="space-y-4 px-6 pb-8 pt-6">
        {isUploadingReadonlyFile ? (
          <div className="mb-0 ml-0 mt-0 flex w-auto gap-4">
            <Progress className="mt-4 h-[0.5rem]" value={uploadProgress} />
          </div>
        ) : fileName ? (
          <div className="mt-2 w-auto p-6 outline outline-1 outline-primary-gray">
            <div className="mb-0 ml-0 mt-0 flex w-auto gap-4">
              <Image src={FileUploadIcon} alt="audio file icon" className="h-auto w-10" />
              <div>
                <p className={`${fredoka.className} font-semibold`}>{fileName}</p>
                <Button variant="ghost" className="flex-start flex justify-between gap-2 px-[0px] hover:bg-transparent" onClick={handleDeleteFile}>
                  <Image src={DeleteIcon} alt="delete button" />
                  <p className={`text-[14px] text-[#FF4B4B] ${bloggerSans.className} `}>Delete</p>
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div
            className={`h-[343px] rounded-[20px] border border-dashed border-primary-green ${dragging && "bg-secondary-yellow"}`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onDragLeave={handleDragLeave}
          >
            <div className="flex h-full flex-col items-center justify-center gap-3 px-5 py-5">
              <Image src={UploadIcon} alt="upload icon" className="h-10 w-10" />
              <p className={`${fredoka.className} font-semibold`}>Drag and Drop file here</p>
              <p>{`jpg, jpeg, png, wav, mp3, m4a, aac, avi, mp4, mpeg formats, up to ${READ_ONLY_FILE_UPLOAD_LIMIT / (1024 * 1024)}MB`}</p>
              <div className="flex items-center gap-2">
                <Separator className="w-28" />
                <p className="text-primary-gray">OR</p>
                <Separator className="w-28" />
              </div>
              <Button
                variant="outline"
                type="button"
                className={`${fredoka.className} flex h-[2.5rem] w-[10rem] items-center justify-center rounded-[10px] px-4 font-semibold`}
                onClick={() => fileInputRef.current?.click()}
              >
                Browse files
              </Button>
              <Input
                type="file"
                accept={"." + VALID_READ_ONLY_FILE_FORMATS.join(",.")}
                ref={fileInputRef}
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderTitleAndFile = () => (
    <div className="relative">
      <div className="space-y-4 pt-6">
        <div className="relative flex h-full items-center">
          <div className="w-full">
            <div className="flex h-full items-center gap-2 px-6">
              <AutosizeTextarea
                className="border-none bg-transparent p-0"
                placeholder="This is a read-only text"
                value={title || ""}
                onChange={handleTitleChange}
              />
            </div>
          </div>
        </div>
        {renderFileOnly()}
      </div>
    </div>
  );

  return <>{category === "Title Only" ? renderTitleOnly() : category === "File Only" ? renderFileOnly() : renderTitleAndFile()}</>;
};

export default ReadOnlyField;
