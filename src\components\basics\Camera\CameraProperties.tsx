import { CameraPropertiesFormData } from "@/app/(core)/forms/types";
import { FormElement } from "@/components/types";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { cameraPropertiesSchema } from "@/schemas/properties/cameraProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const CameraProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id || "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) || "";
  }

  const form = useForm<CameraPropertiesFormData>({
    resolver: zodResolver(cameraPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      minimumDuration: selectedFormBuilderItem?.minimumDuration,
      maximumDuration: selectedFormBuilderItem?.maximumDuration,
      mediaType: selectedFormBuilderItem?.mediaType,
    },
    shouldFocusError: false,
  });

  const {
    getValues,
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("minimumDuration", selectedFormBuilderItem?.minimumDuration || "00:00:02");
    setValue("maximumDuration", selectedFormBuilderItem?.maximumDuration || "00:00:30");
    setValue("mediaType", selectedFormBuilderItem?.mediaType || "Image");
  }, [selectedFormBuilderItem]);

  const applyChanges = (data: CameraPropertiesFormData) => {
    const currentData = { ...getValues() };
    if (currentData.mediaType !== "Video") {
      delete currentData["minimumDuration"];
      delete currentData["maximumDuration"];
    }
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...currentData,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          {getValues().mediaType === "Video" && (
            <>
              <FormField
                control={form.control}
                name="minimumDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Duration</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        onChange={field.onChange}
                        className={`${errors.minimumDuration && "border-destructive"}`}
                        placeholder="hh:mm:ss"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="maximumDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Duration</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        onChange={field.onChange}
                        className={`${errors.maximumDuration && "border-destructive"}`}
                        placeholder="hh:mm:ss"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          <FormField
            control={form.control}
            name="mediaType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Media Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Image">Image</SelectItem>
                    <SelectItem value="Video">Video</SelectItem>
                    <SelectItem value="QR Code">QR Code</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default CameraProperties;
