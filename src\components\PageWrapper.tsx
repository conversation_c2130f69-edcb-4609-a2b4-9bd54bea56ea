import { type PropsWithChildren } from "react";
import TopBar from "./TopBar";
import { TopBarProps } from "@/components/types";

const PageWrapper = ({ children, pageTitle }: PropsWithChildren<TopBarProps>) => {
  return (
    <div className="gap flex flex-col">
      <TopBar pageTitle={pageTitle} />
      <div className="min-h-[100vh]">{children}</div>
    </div>
  );
};

export default PageWrapper;
