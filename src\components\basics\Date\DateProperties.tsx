import { DatePropertiesFormData } from "@/app/(core)/forms/types";
import { FormElement } from "@/components/types";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { ALLOWABLE_DATES, DATE_FORMATS } from "@/lib/constants";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { cn, findFormElementSectionId } from "@/lib/utils";
import { datePropertiesSchema } from "@/schemas/properties/dateProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const DateProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id || "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) || "";
  }

  const form = useForm<DatePropertiesFormData>({
    resolver: zodResolver(datePropertiesSchema),
    mode: "onChange",
    defaultValues: {
      dateFormat: selectedFormBuilderItem?.dateFormat,
      allowableDate: selectedFormBuilderItem?.allowableDate,
      minimumDateRange: selectedFormBuilderItem?.minimumDateRange,
      maximumDateRange: selectedFormBuilderItem?.maximumDateRange,
    },
    shouldFocusError: false,
  });

  const {
    getValues,
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("dateFormat", selectedFormBuilderItem?.dateFormat || "DD-MM-YYYY");
    setValue("allowableDate", selectedFormBuilderItem?.allowableDate || "All Dates");
  }, [selectedFormBuilderItem, setValue]);

  const updateFormElements = (data: DatePropertiesFormData) => {
    const currentData = { ...getValues() };

    if (currentData.allowableDate !== "Date Range") {
      delete currentData["minimumDateRange"];
      delete currentData["maximumDateRange"];
    }
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...currentData,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(updateFormElements)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="dateFormat"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date Format</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {DATE_FORMATS.map(code => (
                        <SelectItem key={code} value={code}>
                          {code}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="allowableDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Allowable date</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {ALLOWABLE_DATES.map(code => (
                        <SelectItem key={code} value={code}>
                          {code}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {getValues("allowableDate") === "Date Range" && (
            <>
              <FormField
                control={form.control}
                name="minimumDateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum</FormLabel>
                    <FormControl>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "active:text-[#75748E]focus:outline-none w-[240px] border-[#75748F] pl-3 text-left font-normal text-[#75748F] hover:border-[#75748F] hover:bg-[#F4F4F8] hover:text-[#75748F] focus:ring-0 active:border-[#75748F] active:bg-[#F4F4F8] active:text-[#75748F]",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? format(new Date(field.value), "dd/MM/yyyy") : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 border-[#75748F] opacity-100" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={date => {
                              if (date) {
                                form.setValue("minimumDateRange", date.toISOString(), { shouldValidate: true });
                                // Trigger form update after setting the value
                                form.handleSubmit(updateFormElements)();
                              }
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="maximumDateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum</FormLabel>
                    <FormControl>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "active:text-[#75748E]focus:outline-none w-[240px] border-[#75748F] pl-3 text-left font-normal text-[#75748F] hover:border-[#75748F] hover:bg-[#F4F4F8] hover:text-[#75748F] focus:ring-0 active:border-[#75748F] active:bg-[#F4F4F8] active:text-[#75748F]",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? format(new Date(field.value), "dd/MM/yyyy") : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-100" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={date => {
                              if (date) {
                                form.setValue("maximumDateRange", date.toISOString(), { shouldValidate: true });
                                // Trigger form update after setting the value
                                form.handleSubmit(updateFormElements)();
                              }
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
        </div>
      </form>
    </Form>
  );
};

export default DateProperties;
