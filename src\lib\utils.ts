import { Screen } from "@/app/(core)/forms/types";
import type { ElementType, FormElement, FormScreen, FormSection } from "@/components/types";
import queryClient from "@/config/tansack-query";
import { store } from "@/lib/redux/store";
import * as changeCase from "change-case";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { replaceFormElement, updateSelectedFormBuilderItem } from "./redux/slices/formSlice";
import { UniqueIdentifier } from "@dnd-kit/core";

export const cn = (...inputs: ClassValue[]) => twMerge(clsx(inputs));

export const getElementType = (elementName: string): ElementType => {
  const suffix = elementName === "Section" ? "Layout" : "Field";
  const elementType = changeCase.pascalCase(elementName) + suffix;
  return elementType as ElementType;
};

export const transformElementType = (elementType: ElementType): string => {
  const elementName = elementType?.endsWith("Field") ? elementType.split("Field") : elementType?.split("Layout");
  return changeCase.snakeCase(elementName?.join(""));
};

export const generateId = (): string => {
  return String(Math.floor(Math.random() * 900000) + 100000); // Generate between 100000 and 999999
};

export const createFormElement = (properties: Omit<FormElement, "id">): FormElement => {
  const element = {
    id: `${transformElementType(properties.type as ElementType)}_${generateId()}`,
    ...properties,
  };
  return element;
};

export const createFormSection = (): FormSection => {
  const section = {
    id: `section_${generateId()}`,
    title: "",
    elements: [],
  };
  return section;
};

export const createNewScreen = () => ({
  id: `screen_${generateId()}`,
  name: "",
  description: "",
  sections: [
    {
      id: `section_${generateId()}`,
      title: "",
      components: [],
    },
  ],
});

export const formatScreensForUI = (data: Screen[]): FormScreen[] => {
  const newScreens = data.map(screen => ({
    ...screen,
    sections: screen.sections.map(section => {
      const { components, ...restSection } = section;
      return {
        ...restSection,
        elements: components.map(component => ({
          ...component,
          type: (changeCase.pascalCase(component.type) + "Field") as ElementType,
        })),
      };
    }),
  }));

  return newScreens;
};

export const formatScreensForApi = (data: FormScreen[]): Screen[] => {
  const newScreens = data.map(screen => ({
    ...screen,
    sections: screen.sections.map(section => {
      const { elements, ...restSection } = section;
      return {
        ...restSection,
        components: elements.map(element => ({
          ...element,
          type: changeCase.snakeCase(element.type.split("Field")[0]) as ElementType,
        })),
      };
    }),
  }));

  return newScreens;
};

export const invalidateQueries = (queryKeys: string | string[]) => {
  if (Array.isArray(queryKeys)) {
    queryKeys.map(key => {
      queryClient.invalidateQueries({ queryKey: [key] });
    });
    return;
  }
  queryClient.invalidateQueries({ queryKey: [queryKeys] });
};

export const getFileFormat = (fileName: string): string => {
  const index = fileName.lastIndexOf(".");
  const extension = fileName.slice(index + 1).toLowerCase();
  return extension;
};

export const updateFormElementOnInputChange = (updateData: Partial<FormElement>, screenId: string, sectionId: string) => {
  const { getState, dispatch } = store;
  const { selectedFormBuilderItem } = getState().form;
  const newFormElement = {
    ...selectedFormBuilderItem,
    ...updateData,
  } as FormElement;
  dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  dispatch(updateSelectedFormBuilderItem(newFormElement));
};

export const swapArrayElements = <T>(array: T[], firstIndex: number, secondIndex: number) => {
  [array[firstIndex], array[secondIndex]] = [array[secondIndex], array[firstIndex]];
};

export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

export const base64ToFile = (base64: string, filename: string, mimeType: string): File => {
  const byteCharacters = atob(base64.split(",")[1]);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new File([byteArray], filename, { type: mimeType });
};

export const findFormSectionScreenId = (screens: FormScreen[], sectionId: string | UniqueIdentifier): string | null => {
  for (const screen of screens) {
    for (const section of screen.sections) {
      if (section.id === sectionId) {
        return screen.id;
      }
    }
  }
  return null;
};

export const findFormElementSectionId = (sections: FormSection[], elementId: string | UniqueIdentifier): string | null => {
  for (const section of sections) {
    for (const element of section.elements) {
      if (element.id === elementId) {
        return section.id;
      }
    }
  }
  return null;
};

export const findFormElementScreenId = (screens: FormScreen[], elementId: string | UniqueIdentifier): string | null => {
  for (const screen of screens) {
    for (const section of screen.sections) {
      for (const element of section.elements) {
        if (element.id === elementId) {
          return screen.id;
        }
      }
    }
  }
  return null;
};

export const getFullReturnUrl = () => {
  // Get current URL from the address bar
  const currentUrl = window.location.href;
  const returnUrl = currentUrl.split("returnUrl=")[1];
  return decodeURIComponent(returnUrl);
};
export const isTagDuplicate = (tag: string, currentElementId: string) => {
  const { formScreens } = store.getState().form;
  const tags: string[] = [];

  formScreens.forEach(screen => {
    screen.sections?.forEach(section => {
      section.elements?.forEach(element => {
        if (element?.tag?.trim() && element?.tag === tag && element.id !== currentElementId) {
          tags.push(element.tag);
        }
      });
    });
  });

  return tags.length > 0;
};

export const getUserTokens = () => {
  return store.getState().auth.userTokens;
};
