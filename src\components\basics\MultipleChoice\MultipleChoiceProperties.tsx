import { MultipleChoicePropertiesFormData, SingleChoicePropertiesFormData } from "@/app/(core)/forms/types";
import AddIcon from "@/assets/icons/add-option.svg";
import RemoveIcon from "@/assets/icons/remove-option.svg";
import ReorderIcon from "@/assets/icons/reorder.svg";
import { FormElement } from "@/components/types";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId, updateFormElementOnInputChange } from "@/lib/utils";
import { multipleChoicePropertiesSchema } from "@/schemas/properties/multipleChoiceProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const MultipleChoiceProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const { options = [] } = selectedFormBuilderItem as FormElement;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id || "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) || "";
  }

  const form = useForm<MultipleChoicePropertiesFormData>({
    resolver: zodResolver(multipleChoicePropertiesSchema),
    mode: "onChange",
    defaultValues: {
      addOther: selectedFormBuilderItem?.addOther,
      addCheckAll: selectedFormBuilderItem?.addCheckAll,
    },
    shouldFocusError: false,
  });

  const { setValue } = form;
  const applyChanges = (data: SingleChoicePropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  useEffect(() => {
    setValue("addOther", selectedFormBuilderItem?.addOther || false);
    setValue("addCheckAll", selectedFormBuilderItem?.addCheckAll || false);
  }, [selectedFormBuilderItem]);

  const handleAddOption = (index: number) => {
    const newOptions = [...options];
    newOptions?.splice(index + 1, 0, "");
    updateFormElementOnInputChange({ options: newOptions }, screenId, sectionId);
  };

  const handleRemoveOption = (index: number) => {
    if (options.length === 1) return;
    const newOptions = [...options];
    newOptions?.splice(index, 1);
    updateFormElementOnInputChange({ options: newOptions }, screenId, sectionId);
  };

  return (
    <>
      <div>
        <p className="mb-2">Options</p>
        <div className="space-y-2">
          {selectedFormBuilderItem?.options?.map((option, i) => (
            <div key={i} className="flex items-center gap-2">
              <div className="flex h-[3rem] w-[8.4rem] items-center overflow-hidden rounded-[10px] border border-primary-gray bg-primary-gray/10 p-3 text-primary-gray">
                {option || `Option ${i + 1}`}
              </div>
              <div className="flex items-center gap-2">
                <Image src={AddIcon} alt="Add Option" className="h-6 w-6 cursor-pointer" onClick={() => handleAddOption(i)} />
                <Image
                  src={RemoveIcon}
                  alt="Remove Option"
                  className={`h-6 w-6 ${options.length > 1 ? "cursor-pointer" : "cursor-not-allowed"}`}
                  onClick={() => handleRemoveOption(i)}
                />
                <Image src={ReorderIcon} alt="Remove Option" className="h-6 w-6 cursor-pointer" />
              </div>
            </div>
          ))}
        </div>
      </div>
      <Form {...form}>
        <form onChange={form.handleSubmit(applyChanges)}>
          <div className="flex flex-col gap-6">
            <div className="flex items-center gap-4">
              <FormField
                control={form.control}
                name="addOther"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Add "Other"</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex items-center gap-4">
              <FormField
                control={form.control}
                name="addCheckAll"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Add "Check All"</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </form>
      </Form>
    </>
  );
};

export default MultipleChoiceProperties;
